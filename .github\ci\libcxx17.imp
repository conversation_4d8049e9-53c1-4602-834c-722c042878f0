[
    # Mappings for libcxx's internal headers
    { include: [ "<__fwd/fstream.h>", private, "<iosfwd>", public ] },
    { include: [ "<__fwd/ios.h>", private, "<iosfwd>", public ] },
    { include: [ "<__fwd/istream.h>", private, "<iosfwd>", public ] },
    { include: [ "<__fwd/ostream.h>", private, "<iosfwd>", public ] },
    { include: [ "<__fwd/sstream.h>", private, "<iosfwd>", public ] },
    { include: [ "<__fwd/streambuf.h>", private, "<iosfwd>", public ] },
    { include: [ "<__fwd/string_view.h>", private, "<string_view>", public ] },
    { include: [ "<__system_error/errc.h>", private, "<system_error>", public ] },

    # Mappings for includes between public headers
    { include: [ "<ios>", public, "<iostream>", public ] },
    { include: [ "<streambuf>", public, "<iostream>", public ] },
    { include: [ "<istream>", public, "<iostream>", public ] },
    { include: [ "<ostream>", public, "<iostream>", public ] },
    { include: [ "<iosfwd>", public, "<iostream>", public ] },

    # Missing mappings in include-what-you-use's libcxx.imp
    { include: ["@<__condition_variable/.*>", private, "<condition_variable>", public ] },
    { include: ["@<__mutex/.*>", private, "<mutex>", public ] },
]
