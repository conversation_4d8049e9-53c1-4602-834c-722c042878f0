name: IWYU
on:
  workflow_call:
jobs:
  Analyzers:
    name: Check includes
    runs-on: ubuntu-22.04
    defaults:
      run:
        working-directory: Stockfish/src
        shell: bash
    steps:
      - name: Checkout Stockfish
        uses: actions/checkout@v4
        with:
          path: Stockfish
          persist-credentials: false

      - name: Checkout include-what-you-use
        uses: actions/checkout@v4
        with:
          repository: include-what-you-use/include-what-you-use
          ref: f25caa280dc3277c4086ec345ad279a2463fea0f
          path: include-what-you-use
          persist-credentials: false

      - name: Download required linux packages
        run: |
          sudo add-apt-repository 'deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-17 main'
          wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key | sudo apt-key add -
          sudo apt update
          sudo apt install -y libclang-17-dev clang-17 libc++-17-dev

      - name: Set up include-what-you-use
        run: |
          mkdir build && cd build
          cmake -G "Unix Makefiles" -DCMAKE_PREFIX_PATH="/usr/lib/llvm-17" ..
          sudo make install
        working-directory: include-what-you-use

      - name: Check include-what-you-use
        run: include-what-you-use --version

      - name: Check includes
        run: >
          make analyze
          COMP=clang
          CXX=include-what-you-use
          CXXFLAGS="-stdlib=libc++ -Xiwyu --comment_style=long -Xiwyu --mapping='${{ github.workspace }}/Stockfish/.github/ci/libcxx17.imp' -Xiwyu --error"
