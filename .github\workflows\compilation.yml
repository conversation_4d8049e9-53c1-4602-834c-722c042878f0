name: Compilation
on:
  workflow_call:
    inputs:
      matrix:
        type: string
        required: true
jobs:
  Compilation:
    name: ${{ matrix.config.name }} ${{ matrix.binaries }}
    runs-on: ${{ matrix.config.os }}
    env:
      COMPCXX: ${{ matrix.config.compiler }}
      COMP: ${{ matrix.config.comp }}
      EXT: ${{ matrix.config.ext }}
      NAME: ${{ matrix.config.simple_name }}
      BINARY: ${{ matrix.binaries }}
      SDE: ${{ matrix.config.sde }}
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(inputs.matrix) }}
    defaults:
      run:
        working-directory: src
        shell: ${{ matrix.config.shell }}
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Install fixed GCC on Linux
        if: runner.os == 'Linux'
        uses: egor-tensin/setup-gcc@eaa888eb19115a521fa72b65cd94fe1f25bbcaac # @v1.3
        with:
          version: 11

      - name: Setup msys and install required packages
        if: runner.os == 'Windows'
        uses: msys2/setup-msys2@v2
        with:
          msystem: ${{ matrix.config.msys_sys }}
          install: mingw-w64-${{ matrix.config.msys_env }} make git zip

      - name: Download SDE package
        if: runner.os == 'Linux' || runner.os == 'Windows'
        uses: petarpetrovt/setup-sde@91a1a03434384e064706634125a15f7446d2aafb # @v2.3
        with:
          environmentVariableName: SDE_DIR
          sdeVersion: 9.27.0

      - name: Download the used network from the fishtest framework
        run: make net

      - name: Check compiler
        run: $COMPCXX -v

      - name: Test help target
        run: make help

      - name: Check git
        run: git --version

      - name: Check compiler
        run: $COMPCXX -v

      - name: Show compiler cpu info
        run: |
          if [[ "$COMPCXX" == clang* ]]; then
             $COMPCXX -E - -march=native -###
          else
            $COMPCXX -Q -march=native --help=target
          fi

      # x86-64 with newer extensions tests

      - name: Compile ${{ matrix.config.binaries }} build
        run: |
          make clean
          make -j4 profile-build ARCH=$BINARY COMP=$COMP WINE_PATH="$SDE"
          make strip ARCH=$BINARY COMP=$COMP
          WINE_PATH="$SDE" ../tests/signature.sh $benchref
          mv ./stockfish$EXT ../stockfish-$NAME-$BINARY$EXT

      - name: Remove non src files
        run: git clean -fx

      - name: Upload artifact for (pre)-release
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.config.simple_name }} ${{ matrix.binaries }}
          path: |
             .
             !.git
             !.output
