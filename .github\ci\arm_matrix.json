{"config": [{"name": "Android NDK aarch64", "os": "ubuntu-22.04", "simple_name": "android", "compiler": "aarch64-linux-android29-clang++", "emu": "qemu-aarch64", "comp": "ndk", "shell": "bash", "archive_ext": "tar"}, {"name": "Android NDK arm", "os": "ubuntu-22.04", "simple_name": "android", "compiler": "armv7a-linux-androideabi29-clang++", "emu": "qemu-arm", "comp": "ndk", "shell": "bash", "archive_ext": "tar"}], "binaries": ["armv8-dotprod", "armv8", "armv7", "armv7-neon"], "exclude": [{"binaries": "armv8-dotprod", "config": {"compiler": "armv7a-linux-androideabi29-clang++"}}, {"binaries": "armv8", "config": {"compiler": "armv7a-linux-androideabi29-clang++"}}, {"binaries": "armv7", "config": {"compiler": "aarch64-linux-android29-clang++"}}, {"binaries": "armv7-neon", "config": {"compiler": "aarch64-linux-android29-clang++"}}]}