# This workflow will run matetrack on the PR

name: Matetrack
on:
  workflow_call:
jobs:
  Matetrack:
    name: Matetrack
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout SF repo 
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          path: <PERSON>fish
          persist-credentials: false

      - name: build SF
        working-directory: Stockfish/src
        run: make -j profile-build

      - name: Checkout matetrack repo
        uses: actions/checkout@v4
        with:
          repository: vondele/matetrack
          path: matetrack
          ref: 4f8a80860ed8f3607f05a9195df8b40203bdc360
          persist-credentials: false

      - name: matetrack install deps
        working-directory: matetrack
        run: pip install -r requirements.txt

      - name: cache syzygy
        id: cache-syzygy
        uses: actions/cache@v4
        with:
           path: |
              matetrack/3-4-5-wdl/
              matetrack/3-4-5-dtz/
           key: key-syzygy

      - name: download syzygy 3-4-5 if needed
        working-directory: matetrack
        if: steps.cache-syzygy.outputs.cache-hit != 'true'
        run: |
          wget --no-verbose -r -nH --cut-dirs=2 --no-parent --reject="index.html*" -e robots=off https://tablebase.lichess.ovh/tables/standard/3-4-5-wdl/
          wget --no-verbose -r -nH --cut-dirs=2 --no-parent --reject="index.html*" -e robots=off https://tablebase.lichess.ovh/tables/standard/3-4-5-dtz/

      - name: Run matetrack
        working-directory: matetrack
        run: |
          python matecheck.py --syzygyPath 3-4-5-wdl/:3-4-5-dtz/ --engine /home/<USER>/work/Stockfish/Stockfish/Stockfish/src/stockfish --epdFile mates2000.epd --nodes 100000 | tee matecheckout.out
          ! grep "issues were detected" matecheckout.out > /dev/null

      - name: Run matetrack with --syzygy50MoveRule false
        working-directory: matetrack
        run: |
          grep 5men cursed.epd > cursed5.epd
          python matecheck.py --syzygyPath 3-4-5-wdl/:3-4-5-dtz/ --engine /home/<USER>/work/Stockfish/Stockfish/Stockfish/src/stockfish --epdFile cursed5.epd --nodes 100000 --syzygy50MoveRule false | tee matecheckcursed.out
          ! grep "issues were detected" matecheckcursed.out > /dev/null

      - name: Verify mate and TB win count for matecheckcursed.out
        working-directory: matetrack
        run: |
          mates=$(grep "Found mates:" matecheckcursed.out | awk '{print $3}')
          tbwins=$(grep "Found TB wins:" matecheckcursed.out | awk '{print $4}')
          if [ $(($mates + $tbwins)) -ne 32 ]; then
            echo "Sum of mates and TB wins is not 32 in matecheckcursed.out" >&2
            exit 1
          fi
