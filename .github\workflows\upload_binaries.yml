name: Upload Binaries
on:
  workflow_call:
    inputs:
      matrix:
        type: string
        required: true
    secrets:
      token:
        required: true

jobs:
  Artifacts:
    name: ${{ matrix.config.name }} ${{ matrix.binaries }}
    runs-on: ${{ matrix.config.os }}
    env:
      COMPCXX: ${{ matrix.config.compiler }}
      COMP: ${{ matrix.config.comp }}
      EXT: ${{ matrix.config.ext }}
      NAME: ${{ matrix.config.simple_name }}
      BINARY: ${{ matrix.binaries }}
      SDE: ${{ matrix.config.sde }}
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(inputs.matrix) }}
    defaults:
      run:
        shell: ${{ matrix.config.shell }}
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Download artifact from compilation
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.config.simple_name }} ${{ matrix.binaries }}
          path: ${{ matrix.config.simple_name }} ${{ matrix.binaries }}

      - name: Setup msys and install required packages
        if: runner.os == 'Windows'
        uses: msys2/setup-msys2@v2
        with:
          msystem: ${{ matrix.config.msys_sys }}
          install: mingw-w64-${{ matrix.config.msys_env }} make git zip

      - name: Create Package
        run: |
          mkdir stockfish

      - name: Download wiki
        run: |
          git clone https://github.com/official-stockfish/Stockfish.wiki.git wiki
          rm -rf wiki/.git
          mv wiki stockfish/

      - name: Copy files
        run: |
          mv "${{ matrix.config.simple_name }} ${{ matrix.binaries }}" stockfish-workflow
          cd stockfish-workflow
          cp -r src ../stockfish/
          cp -r scripts ../stockfish/
          cp stockfish-$NAME-$BINARY$EXT ../stockfish/
          cp "Top CPU Contributors.txt" ../stockfish/
          cp Copying.txt ../stockfish/
          cp AUTHORS ../stockfish/
          cp CITATION.cff ../stockfish/
          cp README.md ../stockfish/
          cp CONTRIBUTING.md ../stockfish/

      - name: Create tar
        if: runner.os != 'Windows'
        run: |
          chmod +x ./stockfish/stockfish-$NAME-$BINARY$EXT
          tar -cvf stockfish-$NAME-$BINARY.tar stockfish

      - name: Create zip
        if: runner.os == 'Windows'
        run: |
          zip -r stockfish-$NAME-$BINARY.zip stockfish

      - name: Release
        if: startsWith(github.ref_name, 'sf_') && github.ref_type == 'tag'
        uses: softprops/action-gh-release@4634c16e79c963813287e889244c50009e7f0981
        with:
          files: stockfish-${{ matrix.config.simple_name }}-${{ matrix.binaries }}.${{ matrix.config.archive_ext }}
          token: ${{ secrets.token }}

      - name: Get last commit sha
        id: last_commit
        run: echo "COMMIT_SHA=$(git rev-parse HEAD | cut -c 1-8)" >> $GITHUB_ENV

      - name: Get commit date
        id: commit_date
        run: echo "COMMIT_DATE=$(git show -s --date=format:'%Y%m%d' --format=%cd HEAD)" >> $GITHUB_ENV

      # Make sure that an old ci that still runs on master doesn't recreate a prerelease
      - name: Check Pullable Commits
        id: check_commits
        run: |
          git fetch
          CHANGES=$(git rev-list HEAD..origin/master --count)
          echo "CHANGES=$CHANGES" >> $GITHUB_ENV

      - name: Prerelease
        if: github.ref_name == 'master' && env.CHANGES == '0'
        continue-on-error: true
        uses: softprops/action-gh-release@4634c16e79c963813287e889244c50009e7f0981
        with:
          name: Stockfish dev-${{ env.COMMIT_DATE }}-${{ env.COMMIT_SHA }}
          tag_name: stockfish-dev-${{ env.COMMIT_DATE }}-${{ env.COMMIT_SHA }}
          prerelease: true
          files: stockfish-${{ matrix.config.simple_name }}-${{ matrix.binaries }}.${{ matrix.config.archive_ext }}
          token: ${{ secrets.token }}
