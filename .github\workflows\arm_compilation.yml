name: Compilation
on:
  workflow_call:
    inputs:
      matrix:
        type: string
        required: true
jobs:
  Compilation:
    name: ${{ matrix.config.name }} ${{ matrix.binaries }}
    runs-on: ${{ matrix.config.os }}
    env:
      COMPCXX: ${{ matrix.config.compiler }}
      COMP: ${{ matrix.config.comp }}
      EMU: ${{ matrix.config.emu }}
      EXT: ${{ matrix.config.ext }}
      BINARY: ${{ matrix.binaries }}
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(inputs.matrix) }}
    defaults:
      run:
        working-directory: src
        shell: ${{ matrix.config.shell }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Download required linux packages
        if: runner.os == 'Linux'
        run: |
          sudo apt update
          sudo apt install qemu-user

      - name: Install NDK
        if: runner.os == 'Linux'
        run: |
          if [ $COMP == ndk ]; then
            NDKV="27.2.12479018"
            ANDROID_ROOT=/usr/local/lib/android
            ANDROID_SDK_ROOT=$ANDROID_ROOT/sdk
            SDKMANAGER=$ANDROID_SDK_ROOT/cmdline-tools/latest/bin/sdkmanager
            echo "y" | $SDKMANAGER "ndk;$NDKV"
            ANDROID_NDK_ROOT=$ANDROID_SDK_ROOT/ndk/$NDKV
            ANDROID_NDK_BIN=$ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64/bin
            echo "ANDROID_NDK_BIN=$ANDROID_NDK_BIN" >> $GITHUB_ENV
          fi

      - name: Extract the bench number from the commit history
        run: |
          for hash in $(git rev-list -100 HEAD); do
            benchref=$(git show -s $hash | tac | grep -m 1 -o -x '[[:space:]]*\b[Bb]ench[ :]\+[1-9][0-9]\{5,7\}\b[[:space:]]*' | sed 's/[^0-9]//g') && break || true
          done
          [[ -n "$benchref" ]] && echo "benchref=$benchref" >> $GITHUB_ENV && echo "From commit: $hash" && echo "Reference bench: $benchref" || echo "No bench found"

      - name: Download the used network from the fishtest framework
        run: make net

      - name: Check compiler
        run: |
          if [ $COMP == ndk ]; then
            export PATH=${{ env.ANDROID_NDK_BIN }}:$PATH
          fi
          $COMPCXX -v

      - name: Test help target
        run: make help

      - name: Check git
        run: git --version

      # Compile profile guided builds

      - name: Compile ${{ matrix.binaries }} build
        run: |
          if [ $COMP == ndk ]; then
            export PATH=${{ env.ANDROID_NDK_BIN }}:$PATH
            export LDFLAGS="-static -Wno-unused-command-line-argument"
          fi
          make clean
          make -j4 profile-build ARCH=$BINARY COMP=$COMP WINE_PATH=$EMU
          make strip ARCH=$BINARY COMP=$COMP
          WINE_PATH=$EMU ../tests/signature.sh $benchref
          mv ./stockfish$EXT ../stockfish-android-$BINARY$EXT

      - name: Remove non src files
        run: git clean -fx

      - name: Upload artifact for (pre)-release
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.config.simple_name }} ${{ matrix.binaries }}
          path: |
            .
            !.git
            !.output
