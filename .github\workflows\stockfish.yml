name: Stockfish
on:
  push:
    tags:
      - "*"
    branches:
      - master
      - tools
      - github_ci
  pull_request:
    branches:
      - master
      - tools
jobs:
  Prerelease:
    if: github.repository == 'official-stockfish/Stockfish' && (github.ref == 'refs/heads/master' || (startsWith(github.ref_name, 'sf_') && github.ref_type == 'tag'))
    runs-on: ubuntu-latest
    permissions:
      contents: write # For deleting/creating a prerelease
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      # returns null if no pre-release exists
      - name: Get Commit SHA of Latest Pre-release
        run: |
          # Install required packages
          sudo apt-get update
          sudo apt-get install -y curl jq

          echo "COMMIT_SHA_TAG=$(jq -r 'map(select(.prerelease)) | first | .tag_name' <<< $(curl -s https://api.github.com/repos/${{ github.repository_owner }}/Stockfish/releases))" >> $GITHUB_ENV

      # delete old previous pre-release and tag
      - run: gh release delete ${{ env.COMMIT_SHA_TAG }} --cleanup-tag
        if: env.COMMIT_SHA_TAG != 'null'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Make sure that an old ci that still runs on master doesn't recreate a prerelease
      - name: Check Pullable Commits
        id: check_commits
        run: |
          git fetch
          CHANGES=$(git rev-list HEAD..origin/master --count)
          echo "CHANGES=$CHANGES" >> $GITHUB_ENV

      - name: Get last commit SHA
        id: last_commit
        run: echo "COMMIT_SHA=$(git rev-parse HEAD | cut -c 1-8)" >> $GITHUB_ENV

      - name: Get commit date
        id: commit_date
        run: echo "COMMIT_DATE=$(git show -s --date=format:'%Y%m%d' --format=%cd HEAD)" >> $GITHUB_ENV

      # Create a new pre-release, the other upload_binaries.yml will upload the binaries
      # to this pre-release.
      - name: Create Prerelease
        if: github.ref_name == 'master' && env.CHANGES == '0'
        uses: softprops/action-gh-release@4634c16e79c963813287e889244c50009e7f0981
        with:
          name: Stockfish dev-${{ env.COMMIT_DATE }}-${{ env.COMMIT_SHA }}
          tag_name: stockfish-dev-${{ env.COMMIT_DATE }}-${{ env.COMMIT_SHA }}
          prerelease: true

  Matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      arm_matrix: ${{ steps.set-arm-matrix.outputs.arm_matrix }}
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - id: set-matrix
        run: |
          TASKS=$(echo $(cat .github/ci/matrix.json) )
          echo "MATRIX=$TASKS" >> $GITHUB_OUTPUT
      - id: set-arm-matrix
        run: |
          TASKS_ARM=$(echo $(cat .github/ci/arm_matrix.json) )
          echo "ARM_MATRIX=$TASKS_ARM" >> $GITHUB_OUTPUT
  Compilation:
    needs: [Matrix]
    uses: ./.github/workflows/compilation.yml
    with:
      matrix: ${{ needs.Matrix.outputs.matrix }}
  ARMCompilation:
    needs: [Matrix]
    uses: ./.github/workflows/arm_compilation.yml
    with:
      matrix: ${{ needs.Matrix.outputs.arm_matrix }}
  IWYU:
    uses: ./.github/workflows/iwyu.yml
  Sanitizers:
    uses: ./.github/workflows/sanitizers.yml
  Tests:
    uses: ./.github/workflows/tests.yml
  Matetrack:
    uses: ./.github/workflows/matetrack.yml
  Games:
    uses: ./.github/workflows/games.yml
  Binaries:
    if: github.repository == 'official-stockfish/Stockfish'
    needs: [Matrix, Prerelease, Compilation]
    uses: ./.github/workflows/upload_binaries.yml
    with:
      matrix: ${{ needs.Matrix.outputs.matrix }}
    permissions:
      contents: write # For deleting/creating a (pre)release
    secrets:
      token: ${{ secrets.GITHUB_TOKEN }}
  ARM_Binaries:
    if: github.repository == 'official-stockfish/Stockfish'
    needs: [Matrix, Prerelease, ARMCompilation]
    uses: ./.github/workflows/upload_binaries.yml
    with:
      matrix: ${{ needs.Matrix.outputs.arm_matrix }}
    permissions:
      contents: write # For deleting/creating a (pre)release
    secrets:
      token: ${{ secrets.GITHUB_TOKEN }}
