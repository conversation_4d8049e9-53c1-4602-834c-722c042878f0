# This workflow will run clang-format and comment on the PR.
# Because of security reasons, it is crucial that this workflow
# executes no shell script nor runs make.
# Read this before editing: https://securitylab.github.com/research/github-actions-preventing-pwn-requests/

name: Clang-Format
on:
  pull_request_target:
    branches:
      - "master"
    paths:
      - "**.cpp"
      - "**.h"

permissions:
  pull-requests: write

jobs:
  Clang-Format:
    name: Clang-Format
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Run clang-format style check
        uses: jidicula/clang-format-action@4726374d1aa3c6aecf132e5197e498979588ebc8 # @v4.15.0
        id: clang-format
        continue-on-error: true
        with:
          clang-format-version: "20"
          exclude-regex: "incbin"

      - name: Comment on PR
        if: steps.clang-format.outcome == 'failure'
        uses: thollander/actions-comment-pull-request@fabd468d3a1a0b97feee5f6b9e499eab0dd903f6 # @v2.5.0
        with:
          message: |
            clang-format 20 needs to be run on this PR.
            If you do not have clang-format installed, the maintainer will run it when merging.
            For the exact version please see https://packages.ubuntu.com/plucky/clang-format-20.

            _(execution **${{ github.run_id }}** / attempt **${{ github.run_attempt }}**)_
          comment_tag: execution
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Comment on PR
        if: steps.clang-format.outcome != 'failure'
        uses: thollander/actions-comment-pull-request@fabd468d3a1a0b97feee5f6b9e499eab0dd903f6 # @v2.5.0
        with:
          message: |
            _(execution **${{ github.run_id }}** / attempt **${{ github.run_attempt }}**)_
          create_if_not_exists: false
          comment_tag: execution
          mode: delete
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
