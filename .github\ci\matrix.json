{"config": [{"name": "Ubuntu 22.04 GCC", "os": "ubuntu-22.04", "simple_name": "ubuntu", "compiler": "g++", "comp": "gcc", "shell": "bash", "archive_ext": "tar", "sde": "/home/<USER>/work/Stockfish/Stockfish/.output/sde-temp-files/sde-external-9.27.0-2023-09-13-lin/sde -future --"}, {"name": "MacOS 13 Apple Clang", "os": "macos-13", "simple_name": "macos", "compiler": "clang++", "comp": "clang", "shell": "bash", "archive_ext": "tar"}, {"name": "MacOS 14 Apple Clang M1", "os": "macos-14", "simple_name": "macos-m1", "compiler": "clang++", "comp": "clang", "shell": "bash", "archive_ext": "tar"}, {"name": "Windows 2022 Mingw-w64 GCC x86_64", "os": "windows-2022", "simple_name": "windows", "compiler": "g++", "comp": "mingw", "msys_sys": "mingw64", "msys_env": "x86_64-gcc", "shell": "msys2 {0}", "ext": ".exe", "sde": "/d/a/Stockfish/Stockfish/.output/sde-temp-files/sde-external-9.27.0-2023-09-13-win/sde.exe -future --", "archive_ext": "zip"}, {"name": "Windows 11 Mingw-w64 Clang arm64", "os": "windows-11-arm", "simple_name": "windows", "compiler": "clang++", "comp": "clang", "msys_sys": "clangarm64", "msys_env": "clang-aarch64-clang", "shell": "msys2 {0}", "ext": ".exe", "archive_ext": "zip"}], "binaries": ["x86-64", "x86-64-sse41-popcnt", "x86-64-avx2", "x86-64-bmi2", "x86-64-avxvnni", "x86-64-avx512", "x86-64-vnni256", "x86-64-vnni512", "apple-silicon", "armv8", "armv8-dotprod"], "exclude": [{"binaries": "x86-64", "config": {"os": "macos-14"}}, {"binaries": "x86-64-sse41-popcnt", "config": {"os": "macos-14"}}, {"binaries": "x86-64-avx2", "config": {"os": "macos-14"}}, {"binaries": "x86-64-bmi2", "config": {"os": "macos-14"}}, {"binaries": "x86-64-avxvnni", "config": {"os": "macos-14"}}, {"binaries": "x86-64-avx512", "config": {"os": "macos-14"}}, {"binaries": "x86-64-vnni256", "config": {"os": "macos-14"}}, {"binaries": "x86-64-vnni512", "config": {"os": "macos-14"}}, {"binaries": "x86-64-avxvnni", "config": {"os": "macos-13"}}, {"binaries": "x86-64-avx512", "config": {"os": "macos-13"}}, {"binaries": "x86-64-vnni256", "config": {"os": "macos-13"}}, {"binaries": "x86-64-vnni512", "config": {"os": "macos-13"}}, {"binaries": "x86-64", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-sse41-popcnt", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-avx2", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-bmi2", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-avxvnni", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-avx512", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-vnni256", "config": {"os": "windows-11-arm"}}, {"binaries": "x86-64-vnni512", "config": {"os": "windows-11-arm"}}, {"binaries": "apple-silicon", "config": {"os": "windows-2022"}}, {"binaries": "apple-silicon", "config": {"os": "windows-11-arm"}}, {"binaries": "apple-silicon", "config": {"os": "ubuntu-20.04"}}, {"binaries": "apple-silicon", "config": {"os": "ubuntu-22.04"}}, {"binaries": "apple-silicon", "config": {"os": "macos-13"}}, {"binaries": "armv8", "config": {"os": "windows-2022"}}, {"binaries": "armv8", "config": {"os": "ubuntu-20.04"}}, {"binaries": "armv8", "config": {"os": "ubuntu-22.04"}}, {"binaries": "armv8", "config": {"os": "macos-13"}}, {"binaries": "armv8", "config": {"os": "macos-14"}}, {"binaries": "armv8-dotprod", "config": {"os": "windows-2022"}}, {"binaries": "armv8-dotprod", "config": {"os": "ubuntu-20.04"}}, {"binaries": "armv8-dotprod", "config": {"os": "ubuntu-22.04"}}, {"binaries": "armv8-dotprod", "config": {"os": "macos-13"}}, {"binaries": "armv8-dotprod", "config": {"os": "macos-14"}}]}